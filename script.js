// Vue.js 应用
new Vue({
    el: '#app',
    data() {
        return {
            // 从外部配置文件加载人员数据
            candidates: personnelConfig.candidates.map(person => ({...person})),
            backupPersonnel: personnelConfig.backupPersonnel.map(person => ({...person}))
        }
    },
    computed: {
        // 合并所有人员
        allPeople() {
            return [...this.candidates, ...this.backupPersonnel];
        },
        // 计算总票数
        totalVotes() {
            return this.allPeople.reduce((total, person) =>
                total + person.votes + person.against + person.abstain, 0);
        },
        // 计算候选人票数
        candidateVotes() {
            return this.candidates.reduce((total, person) =>
                total + person.votes + person.against + person.abstain, 0);
        },
        // 计算备用人员票数
        backupVotes() {
            return this.backupPersonnel.reduce((total, person) =>
                total + person.votes + person.against + person.abstain, 0);
        }
    },
    methods: {
        // 保存数据到本地存储
        saveData() {
            try {
                const dataToSave = {
                    candidates: this.candidates,
                    backupPersonnel: this.backupPersonnel
                };
                localStorage.setItem('votingData', JSON.stringify(dataToSave));
                console.log('数据已保存到本地存储');
            } catch (error) {
                console.error('保存数据失败:', error);
                this.showMessage('error', '保存数据失败');
            }
        },

        // 从本地存储加载数据
        loadData() {
            try {
                const savedData = localStorage.getItem('votingData');
                if (savedData) {
                    const parsedData = JSON.parse(savedData);
                    // 检查新格式数据
                    if (parsedData.candidates && parsedData.backupPersonnel) {
                        // 加载候选人数据
                        this.loadPersonnelData(this.candidates, parsedData.candidates);
                        // 加载备用人员数据
                        this.loadPersonnelData(this.backupPersonnel, parsedData.backupPersonnel);
                        
                        console.log('数据已从本地存储加载');
                        this.showMessage('success', '数据加载成功');
                    }
                }
            } catch (error) {
                console.error('加载数据失败:', error);
                this.showMessage('error', '加载数据失败');
            }
        },
        
        // 辅助方法：加载人员数据
        loadPersonnelData(targetArray, sourceArray) {
            if (Array.isArray(sourceArray) && sourceArray.length === targetArray.length) {
                sourceArray.forEach((person, index) => {
                    if (person.name) {
                        targetArray[index].votes = Math.max(0, person.votes || 0);
                        targetArray[index].against = Math.max(0, person.against || 0);
                        targetArray[index].abstain = Math.max(0, person.abstain || 0);
                    }
                });
            }
        },

        // 导出数据
        exportData() {
            try {
                const dataToExport = {
                    exportTime: new Date().toLocaleString('zh-CN'),
                    totalVotes: this.totalVotes,
                    candidateVotes: this.candidateVotes,
                    backupVotes: this.backupVotes,
                    candidates: this.candidates.map(person => ({
                        name: person.name,
                        votes: person.votes,
                        against: person.against,
                        abstain: person.abstain,
                        percentage: this.totalVotes > 0 ? ((person.votes / this.totalVotes) * 100).toFixed(2) + '%' : '0%'
                    })),
                    backupPersonnel: this.backupPersonnel.map(person => ({
                        name: person.name,
                        votes: person.votes,
                        against: person.against,
                        abstain: person.abstain,
                        percentage: this.totalVotes > 0 ? ((person.votes / this.totalVotes) * 100).toFixed(2) + '%' : '0%'
                    }))
                };

                const dataStr = JSON.stringify(dataToExport, null, 2);
                const dataBlob = new Blob([dataStr], { type: 'application/json' });

                const link = document.createElement('a');
                link.href = URL.createObjectURL(dataBlob);
                link.download = `投票统计_${new Date().toISOString().slice(0, 10)}.json`;
                link.click();

                this.showMessage('success', '数据导出成功');
            } catch (error) {
                console.error('导出数据失败:', error);
                this.showMessage('error', '导出数据失败');
            }
        },

        // 显示消息提示
        showMessage(type, message) {
            this.$message({
                type: type,
                message: message,
                duration: 2000,
                showClose: true
            });
        },

        // 格式化数字显示
        formatNumber(num) {
            return num.toLocaleString();
        },

        // 重置所有票数
        resetAllVotes() {
            this.$confirm('确定要重置所有票数吗？此操作不可撤销。', '确认重置', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // 重置候选人票数
                this.candidates.forEach(person => {
                    person.votes = 0;
                    person.against = 0;
                    person.abstain = 0;
                });

                // 重置备用人员票数
                this.backupPersonnel.forEach(person => {
                    person.votes = 0;
                    person.against = 0;
                    person.abstain = 0;
                });

                // 保存重置后的数据
                this.saveData();

                // 显示成功消息
                this.showMessage('success', '所有票数已重置');
                console.log('所有票数已重置');
            }).catch(() => {
                this.showMessage('info', '已取消重置操作');
            });
        }
    },

    // 组件挂载时加载数据
    mounted() {
        this.loadData();

        // 监听页面关闭前事件，确保数据保存
        window.addEventListener('beforeunload', () => {
            this.saveData();
        });

        // 定期自动保存（每30秒）
        setInterval(() => {
            this.saveData();
        }, 30000);

        console.log('投票统计系统已启动');
        this.showMessage('success', '投票统计系统已启动');
    },

    // 监听数据变化
    watch: {
        candidates: {
            handler() {
                // 当候选人数组发生变化时自动保存
                this.saveData();
            },
            deep: true
        },
        backupPersonnel: {
            handler() {
                // 当备用人员数组发生变化时自动保存
                this.saveData();
            },
            deep: true
        }
    }
});




