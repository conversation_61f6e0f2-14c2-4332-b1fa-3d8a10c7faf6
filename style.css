/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
    background: #f5f7fa;
    min-height: 100vh;
    color: #2c3e50;
    line-height: 1.6;
    font-size: 1.5rem; /* 增加基础字体大小 */
}

/* 主容器 */
.container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 20px;
    min-height: 100vh;
}

/* 头部样式 */
.header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40px;
    background: #ffffff;
    padding: 25px 40px;
    border-radius: 8px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
}

.title {
    font-size: 3.2rem; /* 从2.8rem增加50% */
    color: #2c3e50;
    font-weight: 600;
    letter-spacing: 1px;
}

.title i {
    margin-right: 12px;
    font-size: 4.2rem; /* 从2.8rem增加50% */
    color: #5a6c7d;
}

.stats {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.stats-card {
    border: 1px solid #dcdfe6;
    border-radius: 6px;
    min-width: 160px;
    background: #ffffff;
}

.stats-card.total {
    border-left: 4px solid #409eff;
}

.stats-card.candidate {
    border-left: 4px solid #e6a23c;
}

.stats-card.backup {
    border-left: 4px solid #67c23a;
}

.stats-card .el-card__body {
    padding: 18px 24px;
}

.stats-item {
    display: flex;
    align-items: center;
    color: #2c3e50;
}

.stats-label {
    font-size: 1.4rem; /* 从1.4rem增加50% */
    margin-right: 8px;
    color: #606266;
    font-weight: 500;
}

.stats-value {
    font-size: 3.3rem; /* 从2.2rem增加50% */
    font-weight: 600;
    color: #2c3e50;
}

/* 分组标题 */
.section-header {
    margin: 35px 0 25px 0;
    padding-left: 0;
}

.section-title {
    font-size: 2.7rem; /* 从1.8rem增加50% */
    font-weight: 600;
    color: #2c3e50;
    background: #ffffff;
    padding: 16px 24px;
    border-radius: 6px;
    display: inline-block;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    border: 1px solid #e8eaec;
    letter-spacing: 0.5px;
}

.candidate-header .section-title {
    border-left: 4px solid #e6a23c;
    color: #2c3e50;
}

.backup-header .section-title {
    border-left: 4px solid #67c23a;
    color: #2c3e50;
}

.section-title i {
    margin-right: 8px;
    font-size: 2.4rem; /* 从1.6rem增加50% */
    color: #909399;
}

/* 投票网格 */
.voting-grid {
    display: grid;
    gap: 20px;
    margin-bottom: 30px;
}

/* 候选人网格 - 每行3个 */
.candidate-grid {
    grid-template-columns: repeat(3, 1fr);
}

/* 备用人员网格 - 每行3个（原来是5个） */
.backup-grid {
    grid-template-columns: repeat(3, 1fr);
}

/* 人员卡片 */
.person-card {
    background: #ffffff;
    border-radius: 6px;
    transition: all 0.2s ease;
    border: 1px solid #dcdfe6;
    overflow: hidden;
}

.person-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
    border-color: #c0c4cc;
}

/* 候选人卡片特殊样式 */
.candidate-card {
    border-left: 4px solid #e6a23c;
    position: relative;
}

.candidate-card::before {
    content: '候选人';
    position: absolute;
    top: 12px;
    right: 16px;
    background: #e6a23c;
    color: white;
    font-size: 1.5rem; /* 从1rem增加50% */
    padding: 4px 8px;
    border-radius: 3px;
    font-weight: 500;
}

.candidate-card:hover {
    box-shadow: 0 4px 12px rgba(230, 162, 60, 0.2);
    border-left-color: #d19e2b;
}

/* 备用人员卡片样式 */
.backup-card {
    border-left: 4px solid #67c23a;
}

.backup-card:hover {
    box-shadow: 0 4px 12px rgba(103, 194, 58, 0.15);
    border-left-color: #5daf34;
}

.person-card .el-card__body {
    padding: 25px;
}

.person-info {
    display: flex;
    align-items: center;
    gap: 20px;
}

.person-avatar {
    width: 75px; /* 从50px增加50% */
    height: 75px; /* 从50px增加50% */
    background: #909399;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 2.7rem; /* 从1.8rem增加50% */
    box-shadow: none;
}

.candidate-avatar {
    background: #e6a23c;
}

.backup-avatar {
    background: #67c23a;
}

.person-details {
    flex: 1;
}

.person-name {
    font-size: 2.25rem; /* 从1.5rem增加50% */
    color: #2c3e50;
    margin-bottom: 12px;
    font-weight: 600;
    letter-spacing: 0.5px;
}

.vote-section {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.vote-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-label {
    font-size: 1.8rem; /* 从1.2rem增加50% */
    color: #606266;
    font-weight: 500;
}

.vote-count {
    font-size: 2.7rem; /* 从1.8rem增加50% */
    font-weight: 600;
    color: #409eff;
}

.vote-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* 投票按钮样式 */
.vote-btn {
    width: 40px;
    height: 40px;
    padding: 0;
    margin: 0 4px;
    border-radius: 6px;
    font-size: 1.2rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.vote-btn:first-child {
    margin-left: 0;
}

.vote-btn:last-child {
    margin-right: 0;
}

.vote-btn.is-disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 投票类型样式 */
.vote-type {
    margin-bottom: 1px;
    padding: 8px;
    border-radius: 4px;
    background-color: #f9f9f9;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.vote-type:last-child {
    margin-bottom: 0;
}

.vote-display {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.vote-agree {
    color: #409eff;
}

.vote-against {
    color: #f56c6c;
}

.vote-abstain {
    color: #909399;
}

.vote-label {
    font-size: 1.8rem;
    color: #606266;
    font-weight: 500;
}



.vote-count {
    font-size: 1.8rem;
    font-weight: 600;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .vote-type {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .vote-controls {
        width: 100%;
        justify-content: flex-end;
    }
}

/* 底部按钮 */
.footer {
    display: flex;
    justify-content: center;
    gap: 16px;
    padding: 24px;
    background: #ffffff;
    border-radius: 6px;
    border: 1px solid #e8eaec;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
}

.footer .el-button {
    padding: 10px 20px;
    font-size: 1.8rem; /* 从1.2rem增加50% */
    border-radius: 4px;
    font-weight: 500;
    letter-spacing: 0.5px;
}

/* 响应式设计 */
@media (max-width: 1600px) {
    /* 中等屏幕：候选人2个，备用人员3个（原来是4个） */
    .candidate-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .backup-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (max-width: 1200px) {
    /* 小屏幕：候选人2个，备用人员3个 */
    .candidate-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .backup-grid {
        grid-template-columns: repeat(3, 1fr);
    }

    .title {
        font-size: 2.4rem;
    }

    .container {
        padding: 15px;
    }
}

@media (max-width: 768px) {
    /* 移动端：都是1列 */
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .candidate-grid,
    .backup-grid {
        grid-template-columns: 1fr;
    }

    .person-info {
        flex-direction: column;
        text-align: center;
    }

    .vote-controls {
        justify-content: center;
    }

    .footer {
        flex-direction: column;
        align-items: center;
    }

    .stats {
        justify-content: center;
    }
}

/* Element UI 自定义样式 */
.el-button--primary {
    background: #409eff;
    border-color: #409eff;
}

.el-button--primary:hover {
    background: #66b1ff;
    border-color: #66b1ff;
}

.el-button--danger {
    background: #f56c6c;
    border-color: #f56c6c;
}

.el-button--danger:hover {
    background: #f78989;
    border-color: #f78989;
}

.el-button--warning {
    background: #e6a23c;
    border-color: #e6a23c;
}

.el-button--warning:hover {
    background: #ebb563;
    border-color: #ebb563;
}

.el-button--success {
    background: #67c23a;
    border-color: #67c23a;
}

.el-button--success:hover {
    background: #85ce61;
    border-color: #85ce61;
}

/* 移除了 input-number 相关样式，现在使用按钮控制 */

/* 响应式调整 */
@media (max-width: 1200px) {
    .vote-controls {
        flex-wrap: wrap;
    }
}




