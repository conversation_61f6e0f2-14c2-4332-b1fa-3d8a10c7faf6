<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>支部委员会换届选举投票统计</title>
    <!-- Element UI CSS -->
    <link rel="stylesheet" href="index.css">
    <!-- 自定义样式 -->
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <div id="app">
        <div class="container">
            <div class="header">
                <h1 class="title">
                    <i class="el-icon-pie-chart"></i>
                    支部委员会换届选举投票统计
                </h1>
                <div class="stats">
                    <el-card class="stats-card total">
                        <div class="stats-item">
                            <span class="stats-label">总票数：</span>
                            <span class="stats-value">{{ totalVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card candidate">
                        <div class="stats-item">
                            <span class="stats-label">候选人票数：</span>
                            <span class="stats-value">{{ candidateVotes }}</span>
                        </div>
                    </el-card>
                    <el-card class="stats-card backup">
                        <div class="stats-item">
                            <span class="stats-label">备选人票数：</span>
                            <span class="stats-value">{{ backupVotes }}</span>
                        </div>
                    </el-card>
                </div>
            </div>

            <!-- 候选人区域 -->
      
            <div class="voting-grid candidate-grid">
                <el-card
                    v-for="(person, index) in candidates"
                    :key="'candidate-' + index"
                    class="person-card candidate-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar candidate-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">赞成票：</span>
                                        <span class="vote-count vote-agree">{{ person.votes }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.votes"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                                
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">不赞成：</span>
                                        <span class="vote-count vote-against">{{ person.against }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.against"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                                
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">弃权票：</span>
                                        <span class="vote-count vote-abstain">{{ person.abstain }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.abstain"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <!-- 备用人员区域 -->
            <div class="section-header backup-header">
                <h2 class="section-title">
                    <i class="el-icon-user"></i>
                    备选人
                </h2>
            </div>
            <div class="voting-grid backup-grid">
                <el-card
                    v-for="(person, index) in backupPersonnel"
                    :key="'backup-' + index"
                    class="person-card backup-card"
                    shadow="hover"
                >
                    <div class="person-info">
                        <div class="person-avatar backup-avatar">
                            <i class="el-icon-user-solid"></i>
                        </div>
                        <div class="person-details">
                            <h3 class="person-name">{{ person.name }}</h3>
                            <div class="vote-section">
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">赞成票：</span>
                                        <span class="vote-count vote-agree">{{ person.votes }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.votes"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                                
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">不赞成：</span>
                                        <span class="vote-count vote-against">{{ person.against }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.against"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                                
                                <div class="vote-type">
                                    <div class="vote-display">
                                        <span class="vote-label">弃权票：</span>
                                        <span class="vote-count vote-abstain">{{ person.abstain }}</span>
                                    </div>
                                    <div class="vote-controls">
                                        <el-input-number
                                            v-model="person.abstain"
                                            :min="0"
                                            :max="9999"
                                            size="small"
                                            @change="saveData"
                                            class="vote-input"
                                        ></el-input-number>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </el-card>
            </div>

            <div class="footer">
                <el-button type="warning" @click="resetAllVotes" icon="el-icon-refresh">
                    重置所有票数
                </el-button>
                <el-button type="success" @click="exportData" icon="el-icon-download">
                    导出数据
                </el-button>
            </div>
        </div>
    </div>

    <!-- Vue.js -->
    <script src="vue.js"></script>
    <!-- Element UI JS -->
    <script src="index.js"></script>
    <!-- 人员配置文件 -->
    <script src="personnel.js"></script>
    <!-- 自定义脚本 -->
    <script src="script.js"></script>
</body>
</html>
