<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Personnel.js 测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { color: green; }
        .error { color: red; }
        pre { background: #f5f5f5; padding: 10px; border-radius: 3px; }
    </style>
</head>
<body>
    <h1>Personnel.js 重构测试</h1>
    
    <div class="test-section">
        <h2>测试 1: 基本配置加载</h2>
        <div id="test1-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试 2: 数据结构验证</h2>
        <div id="test2-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试 3: 工具函数测试</h2>
        <div id="test3-result"></div>
    </div>
    
    <div class="test-section">
        <h2>测试 4: 向后兼容性</h2>
        <div id="test4-result"></div>
    </div>

    <script src="personnel.js"></script>
    <script>
        function runTests() {
            // 测试 1: 基本配置加载
            try {
                const test1 = document.getElementById('test1-result');
                if (typeof personnelConfig !== 'undefined' && 
                    personnelConfig.candidates && 
                    personnelConfig.backupPersonnel) {
                    test1.innerHTML = '<span class="success">✓ 配置加载成功</span>';
                    test1.innerHTML += `<br>候选人数量: ${personnelConfig.candidates.length}`;
                    test1.innerHTML += `<br>备用人员数量: ${personnelConfig.backupPersonnel.length}`;
                } else {
                    test1.innerHTML = '<span class="error">✗ 配置加载失败</span>';
                }
            } catch (e) {
                document.getElementById('test1-result').innerHTML = `<span class="error">✗ 错误: ${e.message}</span>`;
            }

            // 测试 2: 数据结构验证
            try {
                const test2 = document.getElementById('test2-result');
                const firstCandidate = personnelConfig.candidates[0];
                const firstBackup = personnelConfig.backupPersonnel[0];
                
                if (firstCandidate.name && 
                    typeof firstCandidate.votes === 'number' &&
                    typeof firstCandidate.against === 'number' &&
                    typeof firstCandidate.abstain === 'number') {
                    test2.innerHTML = '<span class="success">✓ 数据结构正确</span>';
                    test2.innerHTML += '<pre>' + JSON.stringify(firstCandidate, null, 2) + '</pre>';
                } else {
                    test2.innerHTML = '<span class="error">✗ 数据结构错误</span>';
                }
            } catch (e) {
                document.getElementById('test2-result').innerHTML = `<span class="error">✗ 错误: ${e.message}</span>`;
            }

            // 测试 3: 工具函数测试
            try {
                const test3 = document.getElementById('test3-result');
                if (typeof window.PersonnelUtils !== 'undefined') {
                    const utils = window.PersonnelUtils;
                    const testPerson = utils.createPersonWithVotes('测试人员');
                    testPerson.votes = 5;
                    testPerson.against = 2;
                    testPerson.abstain = 1;
                    
                    const totalVotes = utils.getPersonTotalVotes(testPerson);
                    
                    test3.innerHTML = '<span class="success">✓ 工具函数可用</span>';
                    test3.innerHTML += `<br>测试人员总票数: ${totalVotes} (应该是8)`;
                    test3.innerHTML += '<pre>' + JSON.stringify(testPerson, null, 2) + '</pre>';
                } else {
                    test3.innerHTML = '<span class="error">✗ 工具函数不可用</span>';
                }
            } catch (e) {
                document.getElementById('test3-result').innerHTML = `<span class="error">✗ 错误: ${e.message}</span>`;
            }

            // 测试 4: 向后兼容性
            try {
                const test4 = document.getElementById('test4-result');
                // 模拟原始使用方式
                const candidates = personnelConfig.candidates.map(person => ({...person}));
                const backupPersonnel = personnelConfig.backupPersonnel.map(person => ({...person}));
                
                test4.innerHTML = '<span class="success">✓ 向后兼容性良好</span>';
                test4.innerHTML += `<br>可以正常复制候选人数据: ${candidates.length} 人`;
                test4.innerHTML += `<br>可以正常复制备用人员数据: ${backupPersonnel.length} 人`;
                test4.innerHTML += `<br>第一个候选人: ${candidates[0].name}`;
            } catch (e) {
                document.getElementById('test4-result').innerHTML = `<span class="error">✗ 错误: ${e.message}</span>`;
            }
        }

        // 页面加载完成后运行测试
        window.addEventListener('load', runTests);
    </script>
</body>
</html>
