// 人员名单配置 - 简化版本
// 将人员基础信息与投票数据分离，提高可维护性

// 基础人员名单（只包含核心信息）
const PERSONNEL_DATA = {
    // 候选人名单
    candidates: [
        '冯飞',
        '沈俊',
        '杨卫平',
        '栾琛',
        '崔萌萌',
        '傅云峰'
    ],

    // 备用人员名单
    backupPersonnel: [
        '徐海涛', '姜泽', '李毅', '曲兵', '韩吉德', '孙满新',
        '伊燕', '徐麟', '张科首', '郝长平', '崔晓霞', '张军凯',
        '姜峰', '段文明'
    ]
};

// 投票类型配置
const VOTE_TYPES = {
    FOR: 'votes',      // 赞成票
    AGAINST: 'against', // 反对票
    ABSTAIN: 'abstain'  // 弃权票
};

// 工具函数：为人员创建投票数据结构
function createPersonWithVotes(name) {
    return {
        name: name,
        votes: 0,
        against: 0,
        abstain: 0
    };
}

// 工具函数：获取完整的人员配置（包含投票数据）
function getPersonnelConfig() {
    return {
        candidates: PERSONNEL_DATA.candidates.map(createPersonWithVotes),
        backupPersonnel: PERSONNEL_DATA.backupPersonnel.map(createPersonWithVotes)
    };
}

// 工具函数：重置人员投票数据
function resetPersonVotes(person) {
    person.votes = 0;
    person.against = 0;
    person.abstain = 0;
    return person;
}

// 工具函数：获取人员总票数
function getPersonTotalVotes(person) {
    return person.votes + person.against + person.abstain;
}

// 工具函数：添加新人员（用于动态添加）
function addNewPerson(name, type = 'backup') {
    const newPerson = createPersonWithVotes(name);
    if (type === 'candidate') {
        PERSONNEL_DATA.candidates.push(name);
    } else {
        PERSONNEL_DATA.backupPersonnel.push(name);
    }
    return newPerson;
}

// 工具函数：获取所有人员名单（仅姓名）
function getAllPersonnelNames() {
    return {
        candidates: [...PERSONNEL_DATA.candidates],
        backupPersonnel: [...PERSONNEL_DATA.backupPersonnel],
        all: [...PERSONNEL_DATA.candidates, ...PERSONNEL_DATA.backupPersonnel]
    };
}

// 导出配置（保持向后兼容）
const personnelConfig = getPersonnelConfig();

// 如果需要在浏览器控制台中使用，可以将工具函数挂载到全局
if (typeof window !== 'undefined') {
    window.PersonnelUtils = {
        PERSONNEL_DATA,
        VOTE_TYPES,
        createPersonWithVotes,
        getPersonnelConfig,
        resetPersonVotes,
        getPersonTotalVotes,
        addNewPerson,
        getAllPersonnelNames
    };
}